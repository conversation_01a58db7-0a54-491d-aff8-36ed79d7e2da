"""
Answer Generation Pipeline
Generates answers to checklist questions with source references and reasoning
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Import our custom modules
from question_interpreter import QuestionInterpreter, InterpretedQuestion
from data_schema import ConstructionDataStorage, ChecklistAnswer

@dataclass
class SourceReference:
    """Source reference for an answer"""
    document_type: str  # 'specifications', 'drawings'
    document_name: str
    section_title: str
    page_number: int
    content_excerpt: str
    relevance_score: float

@dataclass
class GeneratedAnswer:
    """Generated answer with sources and reasoning"""
    question_id: str
    question_text: str
    answer: str
    confidence: float
    source_references: List[SourceReference]
    reasoning: str
    csi_division: Optional[str] = None
    answer_type: str = "text"  # 'yes_no', 'text', 'not_found'

class AnswerGenerator:
    """Generates answers to checklist questions using extracted document data"""

    def __init__(self):
        self.interpreter = QuestionInterpreter()
        self.storage = ConstructionDataStorage()

        # Load document data
        self.spec_sections = self._load_spec_sections()
        self.contractor_items = self._load_contractor_items()

        # Answer generation patterns
        self.answer_patterns = {
            'yes_no': {
                'positive_indicators': ['included', 'provided', 'furnished', 'specified', 'required'],
                'negative_indicators': ['not included', 'excluded', 'omitted', 'not specified'],
                'uncertain_indicators': ['coordinate', 'verify', 'confirm', 'as required']
            },
            'count': {
                'number_patterns': [r'(\d+)\s*(each|ea|units?|pieces?)', r'quantity[:\s]*(\d+)'],
                'list_indicators': ['schedule', 'list', 'table', 'count']
            },
            'specification': {
                'material_indicators': ['material', 'product', 'manufacturer', 'model', 'type'],
                'requirement_indicators': ['shall', 'must', 'required', 'specified']
            }
        }

    def _load_spec_sections(self) -> List[Dict[str, Any]]:
        """Load specification sections"""
        try:
            with open('parsed_specs/Input - Specifications_parsed.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('sections', [])
        except FileNotFoundError:
            print("Specification sections not found.")
            return []

    def _load_contractor_items(self) -> List[Dict[str, Any]]:
        """Load contractor items from database"""
        try:
            return self.storage.query_items_by_csi_division("")  # Get all items
        except Exception as e:
            print(f"Error loading contractor items: {e}")
            return []

    def generate_answer(self, question_text: str, question_id: str = None) -> GeneratedAnswer:
        """Generate answer for a single question"""

        # Interpret the question
        interpreted = self.interpreter.interpret_question(question_text, question_id)

        # Search for relevant information
        relevant_sources = self._search_relevant_sources(interpreted)

        # Generate answer based on found sources
        answer = self._generate_answer_from_sources(interpreted, relevant_sources)

        return answer

    def _search_relevant_sources(self, interpreted: InterpretedQuestion) -> List[SourceReference]:
        """Search for relevant sources based on interpreted question"""
        sources = []

        # Search in specification sections
        spec_sources = self._search_spec_sections(interpreted)
        sources.extend(spec_sources)

        # Search in contractor items
        item_sources = self._search_contractor_items(interpreted)
        sources.extend(item_sources)

        # Sort by relevance score
        sources.sort(key=lambda x: x.relevance_score, reverse=True)

        return sources[:5]  # Return top 5 most relevant sources

    def _search_spec_sections(self, interpreted: InterpretedQuestion) -> List[SourceReference]:
        """Search specification sections for relevant information"""
        sources = []

        for section in self.spec_sections:
            relevance_score = self._calculate_relevance_score(
                interpreted.search_keywords,
                section['content'],
                interpreted.relevant_csi_divisions,
                section.get('csi_division', '')
            )

            if relevance_score > 0.3:  # Threshold for relevance
                # Extract relevant excerpt
                excerpt = self._extract_relevant_excerpt(
                    section['content'],
                    interpreted.search_keywords
                )

                source = SourceReference(
                    document_type='specifications',
                    document_name='Input - Specifications.pdf',
                    section_title=section['title'],
                    page_number=section['page_numbers'][0] if section['page_numbers'] else 0,
                    content_excerpt=excerpt,
                    relevance_score=relevance_score
                )
                sources.append(source)

        return sources

    def _search_contractor_items(self, interpreted: InterpretedQuestion) -> List[SourceReference]:
        """Search contractor items for relevant information"""
        sources = []

        for item in self.contractor_items:
            relevance_score = self._calculate_relevance_score(
                interpreted.search_keywords,
                item['description'],
                interpreted.relevant_csi_divisions,
                item.get('csi_division', '')
            )

            if relevance_score > 0.3:
                source = SourceReference(
                    document_type='contractor_items',
                    document_name=item['source_document'],
                    section_title=item['title'],
                    page_number=item['page_numbers'][0] if item['page_numbers'] else 0,
                    content_excerpt=item['description'][:300] + "...",
                    relevance_score=relevance_score
                )
                sources.append(source)

        return sources

    def _calculate_relevance_score(self, keywords: List[str], content: str,
                                 target_divisions: List[str], content_division: str) -> float:
        """Calculate relevance score between keywords and content"""
        content_lower = content.lower()
        score = 0.0

        # Keyword matching (0.6 weight)
        keyword_matches = sum(1 for keyword in keywords if keyword.lower() in content_lower)
        if keywords:
            keyword_score = keyword_matches / len(keywords)
            score += keyword_score * 0.6

        # CSI division matching (0.4 weight)
        division_score = 0.0
        if content_division and target_divisions:
            for target_div in target_divisions:
                if target_div.lower() in content_division.lower():
                    division_score = 1.0
                    break
        score += division_score * 0.4

        return min(score, 1.0)

    def _extract_relevant_excerpt(self, content: str, keywords: List[str]) -> str:
        """Extract relevant excerpt from content based on keywords"""
        content_lower = content.lower()

        # Find the best position based on keyword density
        best_start = 0
        best_score = 0
        window_size = 300

        for i in range(0, len(content) - window_size, 50):
            window = content[i:i + window_size].lower()
            score = sum(1 for keyword in keywords if keyword.lower() in window)

            if score > best_score:
                best_score = score
                best_start = i

        # Extract excerpt
        excerpt = content[best_start:best_start + window_size]

        # Clean up excerpt
        if best_start > 0:
            excerpt = "..." + excerpt
        if best_start + window_size < len(content):
            excerpt = excerpt + "..."

        return excerpt.strip()

    def _generate_answer_from_sources(self, interpreted: InterpretedQuestion,
                                    sources: List[SourceReference]) -> GeneratedAnswer:
        """Generate answer based on interpreted question and sources"""

        if not sources:
            return self._generate_not_found_answer(interpreted)

        # Determine answer type based on question
        answer_type = self._determine_answer_type(interpreted.original_question)

        # Generate answer based on type
        if answer_type == 'yes_no':
            answer, reasoning = self._generate_yes_no_answer(interpreted, sources)
        elif answer_type == 'count':
            answer, reasoning = self._generate_count_answer(interpreted, sources)
        else:
            answer, reasoning = self._generate_text_answer(interpreted, sources)

        # Calculate confidence
        confidence = self._calculate_answer_confidence(sources, answer_type)

        return GeneratedAnswer(
            question_id=interpreted.question_id,
            question_text=interpreted.original_question,
            answer=answer,
            confidence=confidence,
            source_references=sources,
            reasoning=reasoning,
            csi_division=interpreted.relevant_csi_divisions[0] if interpreted.relevant_csi_divisions else None,
            answer_type=answer_type
        )

    def _generate_not_found_answer(self, interpreted: InterpretedQuestion) -> GeneratedAnswer:
        """Generate answer when no relevant sources are found"""
        return GeneratedAnswer(
            question_id=interpreted.question_id,
            question_text=interpreted.original_question,
            answer="Information not found in available documents",
            confidence=0.1,
            source_references=[],
            reasoning="No relevant information found in specifications or drawings",
            answer_type="not_found"
        )

    def _determine_answer_type(self, question: str) -> str:
        """Determine the type of answer expected"""
        question_lower = question.lower()

        if any(word in question_lower for word in ['how many', 'count', 'number of']):
            return 'count'
        elif any(word in question_lower for word in ['?', 'or', 'is', 'are', 'confirm']):
            return 'yes_no'
        else:
            return 'text'

    def _generate_yes_no_answer(self, interpreted: InterpretedQuestion,
                               sources: List[SourceReference]) -> Tuple[str, str]:
        """Generate yes/no answer"""
        # Analyze sources for positive/negative indicators
        positive_count = 0
        negative_count = 0

        for source in sources:
            content_lower = source.content_excerpt.lower()

            for indicator in self.answer_patterns['yes_no']['positive_indicators']:
                if indicator in content_lower:
                    positive_count += 1

            for indicator in self.answer_patterns['yes_no']['negative_indicators']:
                if indicator in content_lower:
                    negative_count += 1

        if positive_count > negative_count:
            answer = "Yes"
            reasoning = f"Found {positive_count} positive indicators in specifications"
        elif negative_count > positive_count:
            answer = "No"
            reasoning = f"Found {negative_count} negative indicators in specifications"
        else:
            answer = "Requires verification"
            reasoning = "Mixed or unclear information found in documents"

        return answer, reasoning

    def _generate_count_answer(self, interpreted: InterpretedQuestion,
                              sources: List[SourceReference]) -> Tuple[str, str]:
        """Generate count answer"""
        # Look for numbers in sources
        numbers_found = []

        for source in sources:
            for pattern in self.answer_patterns['count']['number_patterns']:
                matches = re.findall(pattern, source.content_excerpt, re.IGNORECASE)
                numbers_found.extend([match[0] if isinstance(match, tuple) else match for match in matches])

        if numbers_found:
            # Take the most common number or the first one found
            answer = numbers_found[0]
            reasoning = f"Found quantity specification: {answer}"
        else:
            answer = "Count not specified"
            reasoning = "No specific quantities found in available documents"

        return answer, reasoning

    def _generate_text_answer(self, interpreted: InterpretedQuestion,
                             sources: List[SourceReference]) -> Tuple[str, str]:
        """Generate text answer"""
        # Combine information from top sources
        answer_parts = []

        for source in sources[:3]:  # Use top 3 sources
            # Extract key information
            excerpt = source.content_excerpt[:200] + "..."
            answer_parts.append(f"Per {source.section_title}: {excerpt}")

        answer = " | ".join(answer_parts)
        reasoning = f"Based on information from {len(sources)} relevant document sections"

        return answer, reasoning

    def _calculate_answer_confidence(self, sources: List[SourceReference], answer_type: str) -> float:
        """Calculate confidence in the generated answer"""
        if not sources:
            return 0.1

        # Base confidence on source relevance scores
        avg_relevance = sum(source.relevance_score for source in sources) / len(sources)

        # Adjust based on answer type
        type_multipliers = {
            'yes_no': 0.9,
            'count': 0.8,
            'text': 0.7,
            'not_found': 0.1
        }

        confidence = avg_relevance * type_multipliers.get(answer_type, 0.5)

        # Boost confidence if multiple sources agree
        if len(sources) >= 3:
            confidence += 0.1

        return min(confidence, 0.95)  # Cap at 95%

    def generate_answers_batch(self, questions: List[str]) -> List[GeneratedAnswer]:
        """Generate answers for multiple questions"""
        answers = []

        for i, question in enumerate(questions):
            print(f"Processing question {i+1}/{len(questions)}: {question[:50]}...")
            answer = self.generate_answer(question, f"q_{i:03d}")
            answers.append(answer)

        return answers

    def save_answers(self, answers: List[GeneratedAnswer], output_path: str = "generated_answers.json"):
        """Save generated answers to JSON file"""

        # Convert to dictionaries
        answers_dict = []
        for answer in answers:
            answer_dict = asdict(answer)
            # Convert source references
            answer_dict['source_references'] = [asdict(ref) for ref in answer.source_references]
            answers_dict.append(answer_dict)

        # Create summary
        summary = {
            'total_questions': len(answers),
            'by_answer_type': {},
            'by_confidence': {'high': 0, 'medium': 0, 'low': 0},
            'average_confidence': 0.0,
            'questions_with_sources': 0
        }

        # Calculate summary statistics
        total_confidence = 0
        for answer in answers:
            # By answer type
            answer_type = answer.answer_type
            summary['by_answer_type'][answer_type] = summary['by_answer_type'].get(answer_type, 0) + 1

            # By confidence
            if answer.confidence >= 0.7:
                summary['by_confidence']['high'] += 1
            elif answer.confidence >= 0.4:
                summary['by_confidence']['medium'] += 1
            else:
                summary['by_confidence']['low'] += 1

            total_confidence += answer.confidence

            # Questions with sources
            if answer.source_references:
                summary['questions_with_sources'] += 1

        summary['average_confidence'] = total_confidence / len(answers) if answers else 0

        # Save results
        results = {
            'summary': summary,
            'answers': answers_dict
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"Generated answers saved to {output_path}")
        return results

def main():
    """Test the answer generator"""
    generator = AnswerGenerator()

    # Test questions from the assignment
    test_questions = [
        "Entry doors – smart lock or deadbolt?",
        "Inverter or Generator?",
        "Are all fire-rated wall assemblies detailed?",
        "Is ADA compliance for door hardware ensured?",
        "Confirm Eyewash counts. HWH for these if portable, or piping if not.",
        "Anything required for guard shack?",
        "Confirm power wiring for all motorized doors, gates, dock equipment",
        "Who provides unit water sub meters? Who installs?",
        "Confirm make-up air / exhaust fans for proper ventilation included"
    ]

    print("=== Answer Generation Test ===")

    # Generate answers
    answers = generator.generate_answers_batch(test_questions)

    # Save results
    results = generator.save_answers(answers)

    # Display summary
    summary = results['summary']
    print(f"\n=== Answer Generation Summary ===")
    print(f"Total questions: {summary['total_questions']}")
    print(f"Average confidence: {summary['average_confidence']:.2f}")
    print(f"Questions with sources: {summary['questions_with_sources']}")

    print(f"\nBy Answer Type:")
    for answer_type, count in summary['by_answer_type'].items():
        print(f"  {answer_type}: {count}")

    print(f"\nBy Confidence:")
    for level, count in summary['by_confidence'].items():
        print(f"  {level}: {count}")

    # Show sample answers
    print(f"\n=== Sample Answers ===")
    for i, answer in enumerate(answers[:3]):
        print(f"\nQuestion {i+1}: {answer.question_text}")
        print(f"  Answer: {answer.answer}")
        print(f"  Confidence: {answer.confidence:.2f}")
        print(f"  Sources: {len(answer.source_references)}")
        print(f"  Reasoning: {answer.reasoning}")

        if answer.source_references:
            print(f"  Top Source: {answer.source_references[0].section_title}")

if __name__ == "__main__":
    main()