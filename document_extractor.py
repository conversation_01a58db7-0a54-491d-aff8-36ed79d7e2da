"""
Document Analysis and Text Extraction System
Handles OCR, text extraction, and image processing for construction documents
"""

import os
import json
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

import pdfplumber
import PyPDF2
import cv2
import numpy as np
from PIL import Image
import pytesseract
import pandas as pd
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ExtractedText:
    """Container for extracted text with metadata"""
    content: str
    page_number: int
    extraction_method: str
    confidence: float = 0.0
    bbox: Optional[Tuple[float, float, float, float]] = None

@dataclass
class DocumentSection:
    """Container for document sections"""
    title: str
    content: str
    page_range: Tuple[int, int]
    section_type: str  # 'spec', 'drawing', 'schedule', 'detail', etc.
    csi_division: Optional[str] = None

class DocumentExtractor:
    """Main class for document analysis and text extraction"""

    def __init__(self, output_dir: str = "extracted_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Initialize extraction results storage
        self.extracted_texts: List[ExtractedText] = []
        self.document_sections: List[DocumentSection] = []
        self.images: List[Dict] = []

    def extract_from_pdf(self, pdf_path: str, doc_type: str = "unknown") -> Dict[str, Any]:
        """
        Extract text and images from PDF using multiple methods

        Args:
            pdf_path: Path to PDF file
            doc_type: Type of document ('specs', 'drawings', etc.)

        Returns:
            Dictionary containing extracted data
        """
        logger.info(f"Starting extraction from {pdf_path}")

        results = {
            'document_path': pdf_path,
            'document_type': doc_type,
            'texts': [],
            'images': [],
            'sections': [],
            'metadata': {}
        }

        try:
            # Method 1: PyPDF2 for basic text extraction
            results.update(self._extract_with_pypdf2(pdf_path))

            # Method 2: pdfplumber for structured text
            pdfplumber_results = self._extract_with_pdfplumber(pdf_path)
            results['texts'].extend(pdfplumber_results.get('texts', []))

            # Post-process and clean results
            results = self._post_process_results(results, doc_type)

            # Save results
            self._save_results(results, pdf_path)

        except Exception as e:
            logger.error(f"Error extracting from {pdf_path}: {str(e)}")
            results['error'] = str(e)

        return results

    def _extract_with_pypdf2(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using PyPDF2"""
        results = {'texts': [], 'images': [], 'metadata': {}}

        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                results['metadata'] = {
                    'page_count': len(reader.pages),
                    'title': reader.metadata.get('/Title', '') if reader.metadata else '',
                    'author': reader.metadata.get('/Author', '') if reader.metadata else '',
                    'subject': reader.metadata.get('/Subject', '') if reader.metadata else ''
                }

                for page_num, page in enumerate(reader.pages):
                    # Extract text
                    text = page.extract_text()
                    if text.strip():
                        results['texts'].append(ExtractedText(
                            content=text,
                            page_number=page_num + 1,
                            extraction_method='pypdf2_text',
                            confidence=0.8
                        ))

        except Exception as e:
            logger.error(f"PyPDF2 extraction error: {e}")

        return results

    def _extract_with_pdfplumber(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using pdfplumber for better table and layout detection"""
        results = {'texts': []}

        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text with layout preservation
                    text = page.extract_text()
                    if text and text.strip():
                        results['texts'].append(ExtractedText(
                            content=text,
                            page_number=page_num + 1,
                            extraction_method='pdfplumber_text',
                            confidence=0.9
                        ))

                    # Extract tables
                    tables = page.extract_tables()
                    for table_idx, table in enumerate(tables):
                        if table:
                            # Convert table to text format
                            table_text = self._table_to_text(table)
                            results['texts'].append(ExtractedText(
                                content=table_text,
                                page_number=page_num + 1,
                                extraction_method='pdfplumber_table',
                                confidence=0.8
                            ))

        except Exception as e:
            logger.error(f"pdfplumber extraction error: {e}")

        return results

    def _extract_with_ocr(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using OCR for image-based content - simplified version"""
        results = {'texts': [], 'images': []}

        # For now, skip OCR to focus on basic text extraction
        logger.info("OCR extraction skipped in this version")

        return results

    def _table_to_text(self, table: List[List[str]]) -> str:
        """Convert table data to formatted text"""
        if not table:
            return ""

        # Create formatted table text
        lines = []
        for row in table:
            if row:
                # Clean and join cells
                cleaned_row = [str(cell).strip() if cell else "" for cell in row]
                lines.append(" | ".join(cleaned_row))

        return "\n".join(lines)

    def _post_process_results(self, results: Dict[str, Any], doc_type: str) -> Dict[str, Any]:
        """Post-process and clean extraction results"""

        # Deduplicate texts based on content similarity
        unique_texts = []
        seen_content = set()

        for text_obj in results['texts']:
            # Simple deduplication based on first 100 characters
            content_key = text_obj.content[:100].strip().lower()
            if content_key not in seen_content and len(content_key) > 10:
                seen_content.add(content_key)
                unique_texts.append(text_obj)

        results['texts'] = unique_texts

        # Identify document sections based on content patterns
        if doc_type == 'specs':
            results['sections'] = self._identify_spec_sections(results['texts'])
        elif doc_type == 'drawings':
            results['sections'] = self._identify_drawing_sections(results['texts'])

        return results

    def _identify_spec_sections(self, texts: List[ExtractedText]) -> List[DocumentSection]:
        """Identify specification sections and CSI divisions"""
        sections = []

        # Common CSI division patterns
        csi_patterns = {
            r'22\s*00\s*00': '22 00 00 Plumbing',
            r'23\s*00\s*00': '23 00 00 HVAC',
            r'26\s*00\s*00': '26 00 00 Electrical',
            r'08\s*71\s*00': '08 71 00 Door Hardware',
            r'Division\s*(\d+)': 'Division {}'
        }

        for text_obj in texts:
            content = text_obj.content

            # Look for CSI division markers
            for pattern, division_name in csi_patterns.items():
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    # Extract section around the match
                    start = max(0, match.start() - 200)
                    end = min(len(content), match.end() + 1000)
                    section_content = content[start:end]

                    sections.append(DocumentSection(
                        title=division_name,
                        content=section_content,
                        page_range=(text_obj.page_number, text_obj.page_number),
                        section_type='specification',
                        csi_division=division_name
                    ))

        return sections

    def _identify_drawing_sections(self, texts: List[ExtractedText]) -> List[DocumentSection]:
        """Identify drawing sections and components"""
        sections = []

        # Common drawing section patterns
        drawing_patterns = {
            r'FLOOR\s+PLAN': 'Floor Plan',
            r'DETAIL\s+\d+': 'Detail',
            r'SECTION\s+[A-Z]': 'Section',
            r'SCHEDULE': 'Schedule',
            r'LEGEND': 'Legend',
            r'NOTES': 'General Notes'
        }

        for text_obj in texts:
            content = text_obj.content

            for pattern, section_type in drawing_patterns.items():
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    start = max(0, match.start() - 100)
                    end = min(len(content), match.end() + 500)
                    section_content = content[start:end]

                    sections.append(DocumentSection(
                        title=f"{section_type} - Page {text_obj.page_number}",
                        content=section_content,
                        page_range=(text_obj.page_number, text_obj.page_number),
                        section_type='drawing_component'
                    ))

        return sections

    def _save_results(self, results: Dict[str, Any], pdf_path: str):
        """Save extraction results to JSON file"""

        # Convert ExtractedText objects to dictionaries
        serializable_results = results.copy()
        serializable_results['texts'] = [asdict(text) for text in results['texts']]
        serializable_results['sections'] = [asdict(section) for section in results['sections']]

        # Save to JSON
        output_file = self.output_dir / f"{Path(pdf_path).stem}_extracted.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        logger.info(f"Results saved to {output_file}")

def main():
    """Main function to test the extractor"""
    print("Starting document extraction...")
    extractor = DocumentExtractor()

    # Extract from specifications
    if os.path.exists("Input - Specifications.pdf"):
        print("Found specifications PDF, starting extraction...")
        try:
            specs_results = extractor.extract_from_pdf("Input - Specifications.pdf", "specs")
            print(f"Specifications extraction completed. Found {len(specs_results['texts'])} text sections.")
        except Exception as e:
            print(f"Error extracting specifications: {e}")
    else:
        print("Specifications PDF not found")

    # Extract from drawings
    if os.path.exists("Input - Construction Drawings.pdf"):
        print("Found drawings PDF, starting extraction...")
        try:
            drawings_results = extractor.extract_from_pdf("Input - Construction Drawings.pdf", "drawings")
            print(f"Drawings extraction completed. Found {len(drawings_results['texts'])} text sections.")
        except Exception as e:
            print(f"Error extracting drawings: {e}")
    else:
        print("Drawings PDF not found")

if __name__ == "__main__":
    main()