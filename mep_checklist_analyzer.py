"""
MEP Checklist Analyzer
Analyzes MEP checklist items and categorizes by scope, CSI division, and complexity
"""

import pandas as pd
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class ChecklistItem:
    """Structured checklist item"""
    item_id: str
    scope: str  # 'Plumbing', 'HVAC', 'Electrical'
    question_text: str
    csi_division: str
    complexity: str  # 'simple', 'moderate', 'complex'
    keywords: List[str]
    question_type: str  # 'yes_no', 'specification', 'count', 'choice'
    requires_drawing_reference: bool
    requires_spec_reference: bool
    estimated_answer_confidence: float

class MEPChecklistAnalyzer:
    """Analyzer for MEP checklist items"""

    def __init__(self, checklist_path: str = "MEP - Checklist.xlsx"):
        self.checklist_path = checklist_path

        # CSI division mappings
        self.csi_mappings = {
            'plumbing': '22 00 00 Plumbing',
            'hvac': '23 00 00 HVAC',
            'electrical': '26 00 00 Electrical',
            'mechanical': '23 00 00 HVAC'
        }

        # Keywords for categorization
        self.scope_keywords = {
            'Plumbing': [
                'plumbing', 'plumber', 'water', 'drain', 'pipe', 'piping', 'fixture',
                'toilet', 'sink', 'faucet', 'valve', 'pump', 'sump', 'sanitary',
                'storm', 'sewer', 'backflow', 'hydrant', 'fountain', 'disposal',
                'hot water', 'heater', 'hwh', 'gas piping', 'eyewash'
            ],
            'HVAC': [
                'hvac', 'heating', 'ventilation', 'air conditioning', 'duct', 'ductwork',
                'fan', 'exhaust', 'supply', 'return', 'damper', 'filter', 'coil',
                'unit', 'system', 'thermostat', 'control', 'balancing', 'testing',
                'mechanical', 'air', 'ventilating', 'louver', 'grille'
            ],
            'Electrical': [
                'electrical', 'electric', 'power', 'wiring', 'conduit', 'conductor',
                'outlet', 'receptacle', 'switch', 'panel', 'circuit', 'breaker',
                'lighting', 'fixture', 'lamp', 'emergency', 'fire alarm', 'security',
                'communication', 'data', 'telephone', 'cable', 'generator', 'ups',
                'motor', 'starter', 'disconnect', 'transformer', 'grounding'
            ]
        }

        # Complexity indicators
        self.complexity_indicators = {
            'simple': [
                'count', 'how many', 'included', 'provided', 'furnished',
                'yes', 'no', 'confirm'
            ],
            'moderate': [
                'coordinate', 'responsibility', 'who', 'specification',
                'requirement', 'compliance', 'standard'
            ],
            'complex': [
                'design', 'calculation', 'analysis', 'engineering',
                'compatibility', 'integration', 'system', 'performance'
            ]
        }

    def load_checklist(self) -> pd.DataFrame:
        """Load MEP checklist from Excel file"""
        try:
            df = pd.read_excel(self.checklist_path)
            print(f"Loaded checklist with {len(df)} items")
            return df
        except Exception as e:
            print(f"Error loading checklist: {e}")
            return pd.DataFrame()

    def analyze_checklist(self) -> List[ChecklistItem]:
        """Analyze checklist items and categorize them"""
        df = self.load_checklist()
        if df.empty:
            return []

        analyzed_items = []

        for idx, row in df.iterrows():
            # Get the checklist text (assuming it's in the second column)
            question_text = str(row.iloc[1]) if len(row) > 1 else str(row.iloc[0])

            if pd.isna(question_text) or question_text.strip() == '':
                continue

            # Analyze the item
            item = self._analyze_single_item(idx, question_text)
            analyzed_items.append(item)

        print(f"Analyzed {len(analyzed_items)} checklist items")
        return analyzed_items

    def _analyze_single_item(self, item_id: int, question_text: str) -> ChecklistItem:
        """Analyze a single checklist item"""

        # Determine scope
        scope = self._determine_scope(question_text)

        # Determine CSI division
        csi_division = self.csi_mappings.get(scope.lower(), 'Unknown')

        # Determine complexity
        complexity = self._determine_complexity(question_text)

        # Extract keywords
        keywords = self._extract_keywords(question_text)

        # Determine question type
        question_type = self._determine_question_type(question_text)

        # Determine reference requirements
        requires_drawing = self._requires_drawing_reference(question_text)
        requires_spec = self._requires_spec_reference(question_text)

        # Estimate answer confidence
        confidence = self._estimate_answer_confidence(question_text, complexity)

        return ChecklistItem(
            item_id=f"mep_{item_id:03d}",
            scope=scope,
            question_text=question_text.strip(),
            csi_division=csi_division,
            complexity=complexity,
            keywords=keywords,
            question_type=question_type,
            requires_drawing_reference=requires_drawing,
            requires_spec_reference=requires_spec,
            estimated_answer_confidence=confidence
        )

    def _determine_scope(self, question_text: str) -> str:
        """Determine the scope (Plumbing, HVAC, Electrical) of the question"""
        text_lower = question_text.lower()

        scope_scores = {}
        for scope, keywords in self.scope_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            scope_scores[scope] = score

        # Return scope with highest score, or 'General' if no clear match
        if max(scope_scores.values()) > 0:
            return max(scope_scores, key=scope_scores.get)
        else:
            return 'General'

    def _determine_complexity(self, question_text: str) -> str:
        """Determine complexity level of the question"""
        text_lower = question_text.lower()

        complexity_scores = {}
        for complexity, indicators in self.complexity_indicators.items():
            score = sum(1 for indicator in indicators if indicator in text_lower)
            complexity_scores[complexity] = score

        # Return complexity with highest score, default to 'moderate'
        if max(complexity_scores.values()) > 0:
            return max(complexity_scores, key=complexity_scores.get)
        else:
            return 'moderate'

    def _extract_keywords(self, question_text: str) -> List[str]:
        """Extract relevant keywords from the question"""
        text_lower = question_text.lower()
        keywords = []

        # Extract keywords from all scopes
        for scope_keywords in self.scope_keywords.values():
            for keyword in scope_keywords:
                if keyword in text_lower:
                    keywords.append(keyword)

        # Add complexity indicators
        for complexity_keywords in self.complexity_indicators.values():
            for keyword in complexity_keywords:
                if keyword in text_lower:
                    keywords.append(keyword)

        return list(set(keywords))  # Remove duplicates

    def _determine_question_type(self, question_text: str) -> str:
        """Determine the type of question"""
        text_lower = question_text.lower()

        if any(word in text_lower for word in ['how many', 'count', 'number of']):
            return 'count'
        elif any(word in text_lower for word in ['or', 'either', 'which']):
            return 'choice'
        elif any(word in text_lower for word in ['?', 'confirm', 'included', 'provided']):
            return 'yes_no'
        else:
            return 'specification'

    def _requires_drawing_reference(self, question_text: str) -> bool:
        """Determine if question requires drawing reference"""
        text_lower = question_text.lower()

        drawing_indicators = [
            'location', 'where', 'plan', 'drawing', 'detail', 'section',
            'layout', 'floor', 'elevation', 'schedule', 'shown'
        ]

        return any(indicator in text_lower for indicator in drawing_indicators)

    def _requires_spec_reference(self, question_text: str) -> bool:
        """Determine if question requires specification reference"""
        text_lower = question_text.lower()

        spec_indicators = [
            'specification', 'spec', 'requirement', 'standard', 'code',
            'material', 'product', 'manufacturer', 'model', 'type'
        ]

        return any(indicator in text_lower for indicator in spec_indicators)

    def _estimate_answer_confidence(self, question_text: str, complexity: str) -> float:
        """Estimate confidence level for answering the question"""
        base_confidence = {
            'simple': 0.8,
            'moderate': 0.6,
            'complex': 0.4
        }

        confidence = base_confidence.get(complexity, 0.5)

        # Adjust based on question characteristics
        text_lower = question_text.lower()

        # Higher confidence for specific questions
        if any(word in text_lower for word in ['included', 'provided', 'furnished']):
            confidence += 0.1

        # Lower confidence for vague questions
        if any(word in text_lower for word in ['appropriate', 'adequate', 'suitable']):
            confidence -= 0.1

        return min(max(confidence, 0.1), 0.9)  # Clamp between 0.1 and 0.9

    def generate_analysis_summary(self, items: List[ChecklistItem]) -> Dict[str, Any]:
        """Generate summary statistics of the analyzed checklist"""
        if not items:
            return {}

        summary = {
            'total_items': len(items),
            'by_scope': {},
            'by_complexity': {},
            'by_question_type': {},
            'reference_requirements': {
                'drawing_required': 0,
                'spec_required': 0,
                'both_required': 0
            },
            'average_confidence': 0.0,
            'top_keywords': []
        }

        # Count by scope
        for item in items:
            summary['by_scope'][item.scope] = summary['by_scope'].get(item.scope, 0) + 1

        # Count by complexity
        for item in items:
            summary['by_complexity'][item.complexity] = summary['by_complexity'].get(item.complexity, 0) + 1

        # Count by question type
        for item in items:
            summary['by_question_type'][item.question_type] = summary['by_question_type'].get(item.question_type, 0) + 1

        # Count reference requirements
        for item in items:
            if item.requires_drawing_reference:
                summary['reference_requirements']['drawing_required'] += 1
            if item.requires_spec_reference:
                summary['reference_requirements']['spec_required'] += 1
            if item.requires_drawing_reference and item.requires_spec_reference:
                summary['reference_requirements']['both_required'] += 1

        # Calculate average confidence
        summary['average_confidence'] = sum(item.estimated_answer_confidence for item in items) / len(items)

        # Get top keywords
        all_keywords = []
        for item in items:
            all_keywords.extend(item.keywords)

        keyword_counts = {}
        for keyword in all_keywords:
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

        summary['top_keywords'] = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        return summary

    def save_analysis(self, items: List[ChecklistItem], output_path: str = "mep_checklist_analysis.json"):
        """Save analysis results to JSON file"""

        # Convert items to dictionaries
        items_dict = [asdict(item) for item in items]

        # Generate summary
        summary = self.generate_analysis_summary(items)

        # Combine results
        results = {
            'analysis_summary': summary,
            'checklist_items': items_dict
        }

        # Save to file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"Analysis saved to {output_path}")
        return results

def main():
    """Test the MEP checklist analyzer"""
    analyzer = MEPChecklistAnalyzer()

    # Analyze checklist
    items = analyzer.analyze_checklist()

    if items:
        # Generate and save analysis
        results = analyzer.save_analysis(items)

        # Display summary
        summary = results['analysis_summary']
        print(f"\n=== MEP Checklist Analysis Summary ===")
        print(f"Total items: {summary['total_items']}")
        print(f"Average confidence: {summary['average_confidence']:.2f}")

        print(f"\nBy Scope:")
        for scope, count in summary['by_scope'].items():
            print(f"  {scope}: {count}")

        print(f"\nBy Complexity:")
        for complexity, count in summary['by_complexity'].items():
            print(f"  {complexity}: {count}")

        print(f"\nBy Question Type:")
        for qtype, count in summary['by_question_type'].items():
            print(f"  {qtype}: {count}")

        print(f"\nReference Requirements:")
        refs = summary['reference_requirements']
        print(f"  Drawing required: {refs['drawing_required']}")
        print(f"  Spec required: {refs['spec_required']}")
        print(f"  Both required: {refs['both_required']}")

        print(f"\nTop Keywords:")
        for keyword, count in summary['top_keywords'][:5]:
            print(f"  {keyword}: {count}")

        # Show sample items
        print(f"\n=== Sample Items ===")
        for i, item in enumerate(items[:3]):
            print(f"\nItem {i+1}: {item.item_id}")
            print(f"  Scope: {item.scope}")
            print(f"  Question: {item.question_text[:100]}...")
            print(f"  Complexity: {item.complexity}")
            print(f"  Confidence: {item.estimated_answer_confidence:.2f}")

    else:
        print("No checklist items found or error loading checklist")

if __name__ == "__main__":
    main()