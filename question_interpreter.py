"""
Question Interpretation Engine
Interprets vague checklist questions and maps them to relevant document sections
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class InterpretedQuestion:
    """Interpreted question with mapped document sections"""
    question_id: str
    original_question: str
    interpreted_intent: str
    search_keywords: List[str]
    relevant_csi_divisions: List[str]
    document_sections_to_search: List[str]
    search_strategy: str
    confidence: float

@dataclass
class DocumentMapping:
    """Mapping between question and document sections"""
    question_id: str
    document_type: str  # 'specs', 'drawings'
    section_id: str
    relevance_score: float
    matching_keywords: List[str]

class QuestionInterpreter:
    """Interprets vague checklist questions and maps to document sections"""

    def __init__(self):
        # Load analyzed checklist items
        self.checklist_items = self._load_checklist_analysis()

        # Load specification sections
        self.spec_sections = self._load_spec_sections()

        # Question interpretation patterns
        self.interpretation_patterns = {
            # Entry doors patterns
            r'entry\s+doors?\s*[-–]?\s*(smart\s+lock|deadbolt)': {
                'intent': 'Determine type of locking mechanism for entry doors',
                'keywords': ['entry door', 'door hardware', 'lock', 'deadbolt', 'smart lock'],
                'csi_divisions': ['08 71 00 Door Hardware', '08 11 00 Metal Doors'],
                'sections': ['door_schedule', 'hardware_specifications', 'security_requirements']
            },

            # Inverter or Generator patterns
            r'inverter\s+or\s+generator': {
                'intent': 'Determine backup power system type',
                'keywords': ['inverter', 'generator', 'backup power', 'emergency power', 'UPS'],
                'csi_divisions': ['26 00 00 Electrical'],
                'sections': ['electrical_specifications', 'power_systems', 'emergency_systems']
            },

            # Fire-rated assemblies
            r'fire[-\s]?rated\s+(wall\s+)?assembl': {
                'intent': 'Verify fire-rated wall assembly details and specifications',
                'keywords': ['fire-rated', 'fire rating', 'wall assembly', 'fire barrier', 'fire wall'],
                'csi_divisions': ['07 84 00 Firestopping', '09 00 00 Finishes'],
                'sections': ['wall_details', 'fire_protection', 'assembly_details']
            },

            # ADA compliance
            r'ada\s+compliance': {
                'intent': 'Verify ADA compliance requirements and implementation',
                'keywords': ['ADA', 'accessibility', 'compliance', 'disabled access', 'handicap'],
                'csi_divisions': ['08 71 00 Door Hardware', '11 00 00 Equipment'],
                'sections': ['accessibility_requirements', 'door_hardware', 'equipment_specifications']
            },

            # Plumbing fixtures and counts
            r'confirm\s+(.*?)\s+counts?': {
                'intent': 'Verify quantity and specifications of plumbing fixtures',
                'keywords': ['fixture', 'count', 'quantity', 'plumbing'],
                'csi_divisions': ['22 00 00 Plumbing'],
                'sections': ['plumbing_fixtures', 'fixture_schedule', 'plumbing_plans']
            },

            # HVAC systems
            r'(hvac|heating|ventilation|air\s+conditioning)': {
                'intent': 'Verify HVAC system specifications and requirements',
                'keywords': ['HVAC', 'heating', 'ventilation', 'air conditioning', 'mechanical'],
                'csi_divisions': ['23 00 00 HVAC'],
                'sections': ['hvac_specifications', 'mechanical_plans', 'equipment_schedules']
            },

            # Electrical systems
            r'(electrical|power|wiring|conduit)': {
                'intent': 'Verify electrical system specifications and requirements',
                'keywords': ['electrical', 'power', 'wiring', 'conduit', 'circuit'],
                'csi_divisions': ['26 00 00 Electrical'],
                'sections': ['electrical_specifications', 'power_plans', 'electrical_schedules']
            }
        }

        # Common construction terms mapping
        self.term_mappings = {
            'hwh': 'hot water heater',
            'ef': 'exhaust fan',
            'oh doors': 'overhead doors',
            'ahj': 'authority having jurisdiction',
            'vfd': 'variable frequency drive',
            'ups': 'uninterruptible power supply',
            'das': 'distributed antenna system',
            'ev': 'electric vehicle',
            'wap': 'wireless access point'
        }

    def _load_checklist_analysis(self) -> List[Dict[str, Any]]:
        """Load analyzed checklist items"""
        try:
            with open('mep_checklist_analysis.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('checklist_items', [])
        except FileNotFoundError:
            print("Checklist analysis not found. Run mep_checklist_analyzer.py first.")
            return []

    def _load_spec_sections(self) -> List[Dict[str, Any]]:
        """Load specification sections"""
        try:
            with open('parsed_specs/Input - Specifications_parsed.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('sections', [])
        except FileNotFoundError:
            print("Specification sections not found. Run specs_parser.py first.")
            return []

    def interpret_question(self, question_text: str, question_id: str = None) -> InterpretedQuestion:
        """Interpret a single question and map to document sections"""

        if question_id is None:
            question_id = f"q_{hash(question_text) % 10000:04d}"

        # Normalize question text
        normalized_text = self._normalize_text(question_text)

        # Try to match against interpretation patterns
        matched_pattern = self._match_interpretation_pattern(normalized_text)

        if matched_pattern:
            # Use pattern-based interpretation
            interpreted = self._create_interpreted_question_from_pattern(
                question_id, question_text, normalized_text, matched_pattern
            )
        else:
            # Use fallback interpretation
            interpreted = self._create_fallback_interpretation(
                question_id, question_text, normalized_text
            )

        return interpreted

    def _normalize_text(self, text: str) -> str:
        """Normalize text by expanding abbreviations and cleaning"""
        normalized = text.lower().strip()

        # Expand common abbreviations
        for abbrev, full_term in self.term_mappings.items():
            normalized = re.sub(r'\b' + re.escape(abbrev) + r'\b', full_term, normalized)

        return normalized

    def _match_interpretation_pattern(self, text: str) -> Optional[Dict[str, Any]]:
        """Match text against interpretation patterns"""
        for pattern, interpretation in self.interpretation_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                return interpretation
        return None

    def _create_interpreted_question_from_pattern(
        self, question_id: str, original_text: str, normalized_text: str, pattern: Dict[str, Any]
    ) -> InterpretedQuestion:
        """Create interpreted question from matched pattern"""

        return InterpretedQuestion(
            question_id=question_id,
            original_question=original_text,
            interpreted_intent=pattern['intent'],
            search_keywords=pattern['keywords'],
            relevant_csi_divisions=pattern['csi_divisions'],
            document_sections_to_search=pattern['sections'],
            search_strategy='pattern_based',
            confidence=0.8
        )

    def _create_fallback_interpretation(
        self, question_id: str, original_text: str, normalized_text: str
    ) -> InterpretedQuestion:
        """Create fallback interpretation for unmatched questions"""

        # Extract keywords from the question
        keywords = self._extract_question_keywords(normalized_text)

        # Determine likely CSI divisions based on keywords
        csi_divisions = self._infer_csi_divisions(keywords)

        # Determine search sections
        sections = self._infer_document_sections(keywords)

        # Generate intent
        intent = f"Answer question about {', '.join(keywords[:3])}"

        return InterpretedQuestion(
            question_id=question_id,
            original_question=original_text,
            interpreted_intent=intent,
            search_keywords=keywords,
            relevant_csi_divisions=csi_divisions,
            document_sections_to_search=sections,
            search_strategy='keyword_based',
            confidence=0.5
        )

    def _extract_question_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from question text"""
        # Common construction-related keywords
        construction_terms = [
            'door', 'window', 'wall', 'floor', 'ceiling', 'roof', 'foundation',
            'electrical', 'plumbing', 'hvac', 'mechanical', 'structural',
            'fire', 'safety', 'code', 'compliance', 'ada', 'accessibility',
            'material', 'equipment', 'system', 'installation', 'specification',
            'drawing', 'plan', 'detail', 'schedule', 'note', 'requirement'
        ]

        found_keywords = []
        words = text.split()

        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if clean_word in construction_terms:
                found_keywords.append(clean_word)

        # Also look for multi-word terms
        for term in construction_terms:
            if term in text:
                found_keywords.append(term)

        return list(set(found_keywords))  # Remove duplicates

    def _infer_csi_divisions(self, keywords: List[str]) -> List[str]:
        """Infer likely CSI divisions based on keywords"""
        division_keywords = {
            '22 00 00 Plumbing': ['plumbing', 'water', 'drain', 'pipe', 'fixture', 'toilet', 'sink'],
            '23 00 00 HVAC': ['hvac', 'heating', 'ventilation', 'air', 'mechanical', 'duct', 'fan'],
            '26 00 00 Electrical': ['electrical', 'power', 'wiring', 'conduit', 'lighting', 'circuit'],
            '08 71 00 Door Hardware': ['door', 'hardware', 'lock', 'handle', 'hinge'],
            '07 84 00 Firestopping': ['fire', 'rated', 'barrier', 'protection', 'firestopping']
        }

        relevant_divisions = []
        for division, div_keywords in division_keywords.items():
            if any(keyword in keywords for keyword in div_keywords):
                relevant_divisions.append(division)

        return relevant_divisions if relevant_divisions else ['General']

    def _infer_document_sections(self, keywords: List[str]) -> List[str]:
        """Infer document sections to search based on keywords"""
        section_keywords = {
            'specifications': ['material', 'product', 'equipment', 'installation', 'requirement'],
            'schedules': ['schedule', 'count', 'quantity', 'list', 'table'],
            'details': ['detail', 'section', 'assembly', 'construction'],
            'plans': ['plan', 'layout', 'location', 'arrangement'],
            'notes': ['note', 'requirement', 'code', 'compliance', 'standard']
        }

        relevant_sections = []
        for section, sec_keywords in section_keywords.items():
            if any(keyword in keywords for keyword in sec_keywords):
                relevant_sections.append(section)

        return relevant_sections if relevant_sections else ['general']

    def interpret_multiple_questions(self, questions: List[str]) -> List[InterpretedQuestion]:
        """Interpret multiple questions"""
        interpreted_questions = []

        for i, question in enumerate(questions):
            interpreted = self.interpret_question(question, f"q_{i:03d}")
            interpreted_questions.append(interpreted)

        return interpreted_questions

    def save_interpretations(self, interpretations: List[InterpretedQuestion],
                           output_path: str = "question_interpretations.json"):
        """Save interpretations to JSON file"""

        # Convert to dictionaries
        interpretations_dict = [asdict(interp) for interp in interpretations]

        # Create summary
        summary = {
            'total_questions': len(interpretations),
            'by_strategy': {},
            'by_confidence': {'high': 0, 'medium': 0, 'low': 0},
            'common_csi_divisions': {},
            'common_keywords': {}
        }

        # Calculate summary statistics
        for interp in interpretations:
            # By strategy
            strategy = interp.search_strategy
            summary['by_strategy'][strategy] = summary['by_strategy'].get(strategy, 0) + 1

            # By confidence
            if interp.confidence >= 0.7:
                summary['by_confidence']['high'] += 1
            elif interp.confidence >= 0.5:
                summary['by_confidence']['medium'] += 1
            else:
                summary['by_confidence']['low'] += 1

            # CSI divisions
            for division in interp.relevant_csi_divisions:
                summary['common_csi_divisions'][division] = summary['common_csi_divisions'].get(division, 0) + 1

            # Keywords
            for keyword in interp.search_keywords:
                summary['common_keywords'][keyword] = summary['common_keywords'].get(keyword, 0) + 1

        # Sort by frequency
        summary['common_csi_divisions'] = dict(sorted(summary['common_csi_divisions'].items(),
                                                     key=lambda x: x[1], reverse=True))
        summary['common_keywords'] = dict(sorted(summary['common_keywords'].items(),
                                                key=lambda x: x[1], reverse=True))

        # Save results
        results = {
            'summary': summary,
            'interpretations': interpretations_dict
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"Question interpretations saved to {output_path}")
        return results

def main():
    """Test the question interpreter"""
    interpreter = QuestionInterpreter()

    # Test questions from the assignment examples
    test_questions = [
        "Entry doors – smart lock or deadbolt?",
        "Inverter or Generator?",
        "Are all fire-rated wall assemblies detailed?",
        "Is ADA compliance for door hardware ensured?",
        "Confirm Eyewash counts. HWH for these if portable, or piping if not.",
        "Anything required for guard shack?",
        "Confirm power wiring for all motorized doors, gates, dock equipment",
        "Who provides unit water sub meters? Who installs?",
        "Confirm make-up air / exhaust fans for proper ventilation included"
    ]

    print("=== Question Interpretation Test ===")

    # Interpret questions
    interpretations = interpreter.interpret_multiple_questions(test_questions)

    # Save results
    results = interpreter.save_interpretations(interpretations)

    # Display summary
    summary = results['summary']
    print(f"\nInterpretation Summary:")
    print(f"Total questions: {summary['total_questions']}")

    print(f"\nBy Strategy:")
    for strategy, count in summary['by_strategy'].items():
        print(f"  {strategy}: {count}")

    print(f"\nBy Confidence:")
    for level, count in summary['by_confidence'].items():
        print(f"  {level}: {count}")

    print(f"\nTop CSI Divisions:")
    for division, count in list(summary['common_csi_divisions'].items())[:5]:
        print(f"  {division}: {count}")

    print(f"\nTop Keywords:")
    for keyword, count in list(summary['common_keywords'].items())[:10]:
        print(f"  {keyword}: {count}")

    # Show sample interpretations
    print(f"\n=== Sample Interpretations ===")
    for i, interp in enumerate(interpretations[:3]):
        print(f"\nQuestion {i+1}: {interp.original_question}")
        print(f"  Intent: {interp.interpreted_intent}")
        print(f"  Keywords: {', '.join(interp.search_keywords[:5])}")
        print(f"  CSI Divisions: {', '.join(interp.relevant_csi_divisions)}")
        print(f"  Strategy: {interp.search_strategy}")
        print(f"  Confidence: {interp.confidence:.2f}")

if __name__ == "__main__":
    main()