"""
Data Schema and Storage System
Defines structured schemas for construction document data and implements storage
"""

import json
import sqlite3
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime
import pandas as pd

@dataclass
class ContractorItem:
    """Structured data for contractor-relevant items"""
    item_id: str
    csi_division: str
    item_type: str  # 'material', 'equipment', 'procedure', 'requirement'
    title: str
    description: str
    specifications: Dict[str, Any]
    drawing_references: List[str]
    page_numbers: List[int]
    source_document: str
    extraction_confidence: float
    keywords: List[str]

@dataclass
class CrossReference:
    """Cross-references between specs and drawings"""
    reference_id: str
    spec_item_id: str
    drawing_item_id: str
    relationship_type: str  # 'location', 'detail', 'schedule', 'note'
    confidence: float
    description: str

@dataclass
class ChecklistAnswer:
    """Answer to a checklist question with sources"""
    question_id: str
    question_text: str
    answer: str
    confidence: float
    source_references: List[Dict[str, Any]]
    reasoning: str
    csi_division: Optional[str] = None

class ConstructionDataStorage:
    """Storage system for construction document data"""

    def __init__(self, db_path: str = "construction_data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize SQLite database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Contractor items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contractor_items (
                item_id TEXT PRIMARY KEY,
                csi_division TEXT,
                item_type TEXT,
                title TEXT,
                description TEXT,
                specifications TEXT,  -- JSON
                drawing_references TEXT,  -- JSON
                page_numbers TEXT,  -- JSON
                source_document TEXT,
                extraction_confidence REAL,
                keywords TEXT,  -- JSON
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Cross references table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cross_references (
                reference_id TEXT PRIMARY KEY,
                spec_item_id TEXT,
                drawing_item_id TEXT,
                relationship_type TEXT,
                confidence REAL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Checklist answers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS checklist_answers (
                question_id TEXT PRIMARY KEY,
                question_text TEXT,
                answer TEXT,
                confidence REAL,
                source_references TEXT,  -- JSON
                reasoning TEXT,
                csi_division TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Document metadata table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS document_metadata (
                document_id TEXT PRIMARY KEY,
                document_path TEXT,
                document_type TEXT,
                total_pages INTEGER,
                extraction_date TIMESTAMP,
                sections_found INTEGER,
                processing_status TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def store_contractor_items(self, items: List[ContractorItem]):
        """Store contractor items in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for item in items:
            cursor.execute('''
                INSERT OR REPLACE INTO contractor_items
                (item_id, csi_division, item_type, title, description,
                 specifications, drawing_references, page_numbers,
                 source_document, extraction_confidence, keywords)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.item_id,
                item.csi_division,
                item.item_type,
                item.title,
                item.description,
                json.dumps(item.specifications),
                json.dumps(item.drawing_references),
                json.dumps(item.page_numbers),
                item.source_document,
                item.extraction_confidence,
                json.dumps(item.keywords)
            ))

        conn.commit()
        conn.close()
        print(f"Stored {len(items)} contractor items")

    def store_cross_references(self, references: List[CrossReference]):
        """Store cross-references in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for ref in references:
            cursor.execute('''
                INSERT OR REPLACE INTO cross_references
                (reference_id, spec_item_id, drawing_item_id,
                 relationship_type, confidence, description)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                ref.reference_id,
                ref.spec_item_id,
                ref.drawing_item_id,
                ref.relationship_type,
                ref.confidence,
                ref.description
            ))

        conn.commit()
        conn.close()
        print(f"Stored {len(references)} cross-references")

    def store_checklist_answers(self, answers: List[ChecklistAnswer]):
        """Store checklist answers in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for answer in answers:
            cursor.execute('''
                INSERT OR REPLACE INTO checklist_answers
                (question_id, question_text, answer, confidence,
                 source_references, reasoning, csi_division)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                answer.question_id,
                answer.question_text,
                answer.answer,
                answer.confidence,
                json.dumps(answer.source_references),
                answer.reasoning,
                answer.csi_division
            ))

        conn.commit()
        conn.close()
        print(f"Stored {len(answers)} checklist answers")

    def query_items_by_csi_division(self, csi_division: str) -> List[Dict[str, Any]]:
        """Query contractor items by CSI division"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM contractor_items
            WHERE csi_division LIKE ?
        ''', (f'%{csi_division}%',))

        columns = [desc[0] for desc in cursor.description]
        results = []

        for row in cursor.fetchall():
            item = dict(zip(columns, row))
            # Parse JSON fields
            item['specifications'] = json.loads(item['specifications']) if item['specifications'] else {}
            item['drawing_references'] = json.loads(item['drawing_references']) if item['drawing_references'] else []
            item['page_numbers'] = json.loads(item['page_numbers']) if item['page_numbers'] else []
            item['keywords'] = json.loads(item['keywords']) if item['keywords'] else []
            results.append(item)

        conn.close()
        return results

    def query_checklist_answers(self, csi_division: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query checklist answers, optionally filtered by CSI division"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if csi_division:
            cursor.execute('''
                SELECT * FROM checklist_answers
                WHERE csi_division LIKE ?
            ''', (f'%{csi_division}%',))
        else:
            cursor.execute('SELECT * FROM checklist_answers')

        columns = [desc[0] for desc in cursor.description]
        results = []

        for row in cursor.fetchall():
            answer = dict(zip(columns, row))
            # Parse JSON field
            answer['source_references'] = json.loads(answer['source_references']) if answer['source_references'] else []
            results.append(answer)

        conn.close()
        return results

    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        stats = {}

        # Count contractor items
        cursor.execute('SELECT COUNT(*) FROM contractor_items')
        stats['contractor_items'] = cursor.fetchone()[0]

        # Count cross references
        cursor.execute('SELECT COUNT(*) FROM cross_references')
        stats['cross_references'] = cursor.fetchone()[0]

        # Count checklist answers
        cursor.execute('SELECT COUNT(*) FROM checklist_answers')
        stats['checklist_answers'] = cursor.fetchone()[0]

        # Count unique CSI divisions
        cursor.execute('SELECT COUNT(DISTINCT csi_division) FROM contractor_items')
        stats['unique_csi_divisions'] = cursor.fetchone()[0]

        conn.close()
        return stats

class DataProcessor:
    """Process extracted data into structured format"""

    def __init__(self, storage: ConstructionDataStorage):
        self.storage = storage

    def process_spec_data(self, spec_data: Dict[str, Any]) -> List[ContractorItem]:
        """Process specification data into contractor items"""
        items = []

        for section in spec_data.get('sections', []):
            # Create contractor item from spec section
            item_id = f"spec_{section['csi_division'].replace(' ', '_')}_{hash(section['title']) % 10000}"

            # Extract keywords from content
            keywords = self._extract_keywords(section['content'])

            # Determine item type based on content
            item_type = self._classify_item_type(section['content'])

            item = ContractorItem(
                item_id=item_id,
                csi_division=section['csi_division'],
                item_type=item_type,
                title=section['title'],
                description=section['content'][:500] + "..." if len(section['content']) > 500 else section['content'],
                specifications={'subsections': section.get('subsections', [])},
                drawing_references=[],
                page_numbers=section['page_numbers'],
                source_document=spec_data['document_path'],
                extraction_confidence=0.8,
                keywords=keywords
            )

            items.append(item)

        return items

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract relevant keywords from content"""
        # Common construction keywords
        construction_keywords = [
            'fire-rated', 'ADA', 'compliance', 'door', 'hardware', 'electrical', 'plumbing',
            'HVAC', 'mechanical', 'installation', 'material', 'equipment', 'specification',
            'requirement', 'code', 'standard', 'testing', 'inspection', 'warranty',
            'maintenance', 'operation', 'safety', 'structural', 'concrete', 'steel',
            'insulation', 'waterproofing', 'roofing', 'flooring', 'ceiling', 'wall'
        ]

        found_keywords = []
        content_lower = content.lower()

        for keyword in construction_keywords:
            if keyword in content_lower:
                found_keywords.append(keyword)

        return found_keywords[:10]  # Limit to top 10

    def _classify_item_type(self, content: str) -> str:
        """Classify item type based on content"""
        content_lower = content.lower()

        if any(word in content_lower for word in ['material', 'product', 'component']):
            return 'material'
        elif any(word in content_lower for word in ['equipment', 'system', 'unit']):
            return 'equipment'
        elif any(word in content_lower for word in ['installation', 'execution', 'procedure']):
            return 'procedure'
        else:
            return 'requirement'

def main():
    """Test the data schema and storage system"""
    # Initialize storage
    storage = ConstructionDataStorage()
    processor = DataProcessor(storage)

    # Load and process spec data if available
    spec_file = Path("parsed_specs/Input - Specifications_parsed.json")
    if spec_file.exists():
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_data = json.load(f)

        # Process into contractor items
        items = processor.process_spec_data(spec_data)

        # Store in database
        storage.store_contractor_items(items)

        # Show stats
        stats = storage.get_database_stats()
        print(f"\nDatabase Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

        # Show some sample items
        sample_items = storage.query_items_by_csi_division("22 00 00")
        print(f"\nSample Plumbing Items: {len(sample_items)}")
        for item in sample_items[:3]:
            print(f"  - {item['title'][:50]}...")

    else:
        print("No specification data found. Run specs_parser.py first.")

if __name__ == "__main__":
    main()