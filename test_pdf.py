import PyPDF2
import os

def test_pdf_reading():
    """Test basic PDF reading"""
    
    if os.path.exists("Input - Specifications.pdf"):
        print("Testing Specifications PDF...")
        try:
            with open("Input - Specifications.pdf", "rb") as file:
                reader = PyPDF2.PdfReader(file)
                print(f"Number of pages: {len(reader.pages)}")
                
                # Try to read first page
                if len(reader.pages) > 0:
                    first_page = reader.pages[0]
                    text = first_page.extract_text()
                    print(f"First page text length: {len(text)}")
                    print(f"First 200 characters: {text[:200]}")
                    
        except Exception as e:
            print(f"Error reading specifications: {e}")

if __name__ == "__main__":
    test_pdf_reading()
