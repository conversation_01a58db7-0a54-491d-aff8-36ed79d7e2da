"""
Comprehensive Demo of the Construction Document Analysis System
Demonstrates end-to-end data extraction and checklist answering
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any

# Import our modules
from specs_parser import SpecificationsParser
from data_schema import ConstructionDataStorage, DataProcessor
from mep_checklist_analyzer import MEPChecklistAnalyzer
from question_interpreter import QuestionInterpreter
from answer_generator import AnswerGenerator

class ConstructionAnalysisDemo:
    """Comprehensive demo of the construction document analysis system"""

    def __init__(self):
        self.output_dir = Path("demo_output")
        self.output_dir.mkdir(exist_ok=True)

        # Initialize components
        self.specs_parser = SpecificationsParser()
        self.storage = ConstructionDataStorage()
        self.data_processor = DataProcessor(self.storage)
        self.checklist_analyzer = MEPChecklistAnalyzer()
        self.question_interpreter = QuestionInterpreter()
        self.answer_generator = AnswerGenerator()

        # Demo questions (from assignment examples)
        self.demo_questions = [
            "Entry doors – smart lock or deadbolt?",
            "Inverter or Generator?",
            "Are all fire-rated wall assemblies detailed?",
            "Is ADA compliance for door hardware ensured?",
            "Confirm Eyewash counts. HWH for these if portable, or piping if not.",
            "Anything required for guard shack?",
            "Confirm power wiring for all motorized doors, gates, dock equipment",
            "Who provides unit water sub meters? Who installs?",
            "Confirm make-up air / exhaust fans for proper ventilation included",
            "Are door hardware specifications complete for all door types?"
        ]

    def run_full_demo(self):
        """Run the complete demonstration"""
        print("=" * 80)
        print("CONSTRUCTION DOCUMENT ANALYSIS SYSTEM - COMPREHENSIVE DEMO")
        print("=" * 80)

        # Step 1: Parse specifications
        print("\n1. PARSING SPECIFICATIONS...")
        spec_results = self._demo_spec_parsing()

        # Step 2: Process data into structured format
        print("\n2. PROCESSING DATA INTO STRUCTURED FORMAT...")
        self._demo_data_processing(spec_results)

        # Step 3: Analyze MEP checklist
        print("\n3. ANALYZING MEP CHECKLIST...")
        checklist_results = self._demo_checklist_analysis()

        # Step 4: Interpret questions
        print("\n4. INTERPRETING DEMO QUESTIONS...")
        interpretation_results = self._demo_question_interpretation()

        # Step 5: Generate answers
        print("\n5. GENERATING ANSWERS...")
        answer_results = self._demo_answer_generation()

        # Step 6: Create comprehensive report
        print("\n6. CREATING COMPREHENSIVE REPORT...")
        self._create_demo_report(spec_results, checklist_results,
                               interpretation_results, answer_results)

        print("\n" + "=" * 80)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print(f"All results saved to: {self.output_dir}")

        return {
            'specifications': spec_results,
            'checklist': checklist_results,
            'interpretations': interpretation_results,
            'answers': answer_results
        }

    def _demo_spec_parsing(self) -> Dict[str, Any]:
        """Demonstrate specification parsing"""
        if not os.path.exists("Input - Specifications.pdf"):
            print("  ❌ Specifications PDF not found")
            return {}

        print("  📄 Parsing specifications PDF...")
        results = self.specs_parser.parse_specifications("Input - Specifications.pdf")

        print(f"  ✅ Found {results['sections_found']} specification sections")
        print(f"  📊 CSI divisions identified: {len(results['csi_divisions'])}")

        # Show sample sections
        print("  📋 Sample sections:")
        for i, section in enumerate(results['sections'][:3]):
            print(f"    {i+1}. {section['csi_division']} - {section['title'][:50]}...")

        return results

    def _demo_data_processing(self, spec_results: Dict[str, Any]):
        """Demonstrate data processing"""
        if not spec_results:
            print("  ❌ No specification data to process")
            return

        print("  🔄 Processing specifications into contractor items...")
        contractor_items = self.data_processor.process_spec_data(spec_results)

        print("  💾 Storing in database...")
        self.storage.store_contractor_items(contractor_items)

        # Show database stats
        stats = self.storage.get_database_stats()
        print(f"  ✅ Stored {stats['contractor_items']} contractor items")
        print(f"  📊 Unique CSI divisions: {stats['unique_csi_divisions']}")

        # Show sample items
        sample_items = self.storage.query_items_by_csi_division("22 00 00")
        if sample_items:
            print(f"  🔧 Sample plumbing items: {len(sample_items)}")
            for item in sample_items[:2]:
                print(f"    - {item['title'][:60]}...")

    def _demo_checklist_analysis(self) -> Dict[str, Any]:
        """Demonstrate checklist analysis"""
        if not os.path.exists("MEP - Checklist.xlsx"):
            print("  ❌ MEP Checklist not found")
            return {}

        print("  📋 Analyzing MEP checklist...")
        checklist_items = self.checklist_analyzer.analyze_checklist()

        if not checklist_items:
            print("  ❌ No checklist items found")
            return {}

        results = self.checklist_analyzer.save_analysis(checklist_items,
                                                       str(self.output_dir / "checklist_analysis.json"))

        summary = results['analysis_summary']
        print(f"  ✅ Analyzed {summary['total_items']} checklist items")
        print(f"  📊 By scope: {dict(list(summary['by_scope'].items())[:3])}")
        print(f"  🎯 Average confidence: {summary['average_confidence']:.2f}")

        return results

    def _demo_question_interpretation(self) -> Dict[str, Any]:
        """Demonstrate question interpretation"""
        print("  🤔 Interpreting demo questions...")

        interpretations = self.question_interpreter.interpret_multiple_questions(self.demo_questions)
        results = self.question_interpreter.save_interpretations(
            interpretations, str(self.output_dir / "question_interpretations.json")
        )

        summary = results['summary']
        print(f"  ✅ Interpreted {summary['total_questions']} questions")
        print(f"  📊 By strategy: {summary['by_strategy']}")
        print(f"  🎯 High confidence: {summary['by_confidence']['high']}")

        # Show sample interpretations
        print("  📝 Sample interpretations:")
        for i, interp in enumerate(interpretations[:2]):
            print(f"    {i+1}. {interp.original_question[:50]}...")
            print(f"       Intent: {interp.interpreted_intent[:60]}...")
            print(f"       CSI: {', '.join(interp.relevant_csi_divisions[:2])}")

        return results