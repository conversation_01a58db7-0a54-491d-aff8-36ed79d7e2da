"""
Specifications Parser
Parses and structures specification documents by CSI divisions
"""

import os
import json
import re
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import PyPDF2

@dataclass
class SpecSection:
    """Container for specification sections"""
    csi_division: str
    title: str
    content: str
    page_numbers: List[int]
    section_number: Optional[str] = None
    subsections: List[str] = None

class SpecificationsParser:
    """Parser for construction specifications"""

    def __init__(self, output_dir: str = "parsed_specs"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # CSI Division patterns
        self.csi_patterns = {
            r'22\s*00\s*00': '22 00 00 Plumbing',
            r'23\s*00\s*00': '23 00 00 HVAC',
            r'26\s*00\s*00': '26 00 00 Electrical',
            r'08\s*71\s*00': '08 71 00 Door Hardware',
            r'08\s*11\s*00': '08 11 00 Metal Doors and Frames',
            r'09\s*91\s*00': '09 91 00 Painting',
            r'03\s*30\s*00': '03 30 00 Cast-in-Place Concrete',
            r'07\s*21\s*00': '07 21 00 Thermal Insulation',
            r'Division\s*(\d+)': 'Division {}',
            r'SECTION\s*(\d{2}\s*\d{2}\s*\d{2})': 'Section {}'
        }

        # Common specification keywords
        self.spec_keywords = [
            'GENERAL', 'PRODUCTS', 'EXECUTION', 'SUBMITTALS', 'QUALITY ASSURANCE',
            'DELIVERY', 'STORAGE', 'HANDLING', 'WARRANTY', 'MAINTENANCE',
            'MATERIALS', 'EQUIPMENT', 'INSTALLATION', 'FIELD QUALITY CONTROL'
        ]

    def parse_specifications(self, pdf_path: str) -> Dict[str, Any]:
        """
        Parse specifications PDF and extract structured data

        Args:
            pdf_path: Path to specifications PDF

        Returns:
            Dictionary containing parsed specification data
        """
        print(f"Parsing specifications from {pdf_path}")

        # Extract text from PDF
        pages_text = self._extract_text_from_pdf(pdf_path)

        # Find CSI divisions and sections
        sections = self._identify_csi_sections(pages_text)

        # Parse each section for detailed content
        parsed_sections = []
        for section in sections:
            parsed_section = self._parse_section_content(section)
            parsed_sections.append(parsed_section)

        results = {
            'document_path': pdf_path,
            'total_pages': len(pages_text),
            'sections_found': len(parsed_sections),
            'sections': [asdict(s) for s in parsed_sections],
            'csi_divisions': list(set([s.csi_division for s in parsed_sections]))
        }

        # Save results
        self._save_results(results, pdf_path)

        return results

    def _extract_text_from_pdf(self, pdf_path: str) -> List[Tuple[int, str]]:
        """Extract text from each page of the PDF"""
        pages_text = []

        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)

                for page_num, page in enumerate(reader.pages):
                    text = page.extract_text()
                    if text.strip():
                        pages_text.append((page_num + 1, text))

        except Exception as e:
            print(f"Error extracting text: {e}")

        return pages_text

    def _identify_csi_sections(self, pages_text: List[Tuple[int, str]]) -> List[SpecSection]:
        """Identify CSI divisions and sections in the text"""
        sections = []

        for page_num, text in pages_text:
            # Look for CSI division patterns
            for pattern, division_template in self.csi_patterns.items():
                matches = re.finditer(pattern, text, re.IGNORECASE)

                for match in matches:
                    # Extract section around the match
                    start = max(0, match.start() - 500)
                    end = min(len(text), match.end() + 2000)
                    section_content = text[start:end]

                    # Determine division name
                    if '{}' in division_template:
                        division_name = division_template.format(match.group(1))
                    else:
                        division_name = division_template

                    # Create section
                    section = SpecSection(
                        csi_division=division_name,
                        title=self._extract_section_title(section_content),
                        content=section_content,
                        page_numbers=[page_num],
                        section_number=match.group(0) if match.groups() else None
                    )

                    sections.append(section)

        # Merge duplicate sections
        sections = self._merge_duplicate_sections(sections)

        return sections

    def _extract_section_title(self, content: str) -> str:
        """Extract section title from content"""
        lines = content.split('\n')

        # Look for title-like lines (all caps, short)
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line and len(line) < 100 and line.isupper():
                return line

        # Fallback to first non-empty line
        for line in lines:
            line = line.strip()
            if line:
                return line[:50] + "..." if len(line) > 50 else line

        return "Unknown Section"

    def _merge_duplicate_sections(self, sections: List[SpecSection]) -> List[SpecSection]:
        """Merge sections with the same CSI division"""
        merged = {}

        for section in sections:
            key = section.csi_division
            if key in merged:
                # Merge content and page numbers
                merged[key].content += "\n\n" + section.content
                merged[key].page_numbers.extend(section.page_numbers)
                merged[key].page_numbers = list(set(merged[key].page_numbers))
            else:
                merged[key] = section

        return list(merged.values())

    def _parse_section_content(self, section: SpecSection) -> SpecSection:
        """Parse section content for subsections and key information"""
        content = section.content
        subsections = []

        # Look for subsection headers
        for keyword in self.spec_keywords:
            if keyword in content.upper():
                subsections.append(keyword)

        section.subsections = subsections
        return section

    def _save_results(self, results: Dict[str, Any], pdf_path: str):
        """Save parsing results to JSON file"""

        # Results are already serializable
        output_file = self.output_dir / f"{Path(pdf_path).stem}_parsed.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"Parsing results saved to {output_file}")

def main():
    """Test the specifications parser"""
    parser = SpecificationsParser()

    if os.path.exists("Input - Specifications.pdf"):
        results = parser.parse_specifications("Input - Specifications.pdf")
        print(f"\nParsing completed!")
        print(f"Total pages: {results['total_pages']}")
        print(f"Sections found: {results['sections_found']}")
        print(f"CSI divisions found: {results['csi_divisions']}")

        # Show first few sections
        for i, section_dict in enumerate(results['sections'][:5]):
            print(f"\nSection {i+1}: {section_dict['csi_division']}")
            print(f"Title: {section_dict['title']}")
            print(f"Pages: {section_dict['page_numbers']}")
            print(f"Subsections: {section_dict['subsections']}")
    else:
        print("Specifications PDF not found")

if __name__ == "__main__":
    main()